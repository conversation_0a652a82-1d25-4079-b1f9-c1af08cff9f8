#!/usr/bin/env php
<?php

use Shopware\Core\Framework\Adapter\Kernel\KernelFactory;
use Shopware\Core\Framework\Plugin\KernelPluginLoader\DbalKernelPluginLoader;
use Shopware\Core\Framework\Plugin\KernelPluginLoader\StaticKernelPluginLoader;
use Shopware\Core\Kernel;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArgvInput;

$envFile = __DIR__ . '/../.env';

if (!file_exists(__DIR__ . '/../.env') && !file_exists(__DIR__ . '/../.env.dist') && !file_exists(__DIR__ . '/../.env.local.php')) {
    $_SERVER['APP_RUNTIME_OPTIONS']['disable_dotenv'] = true;
}

require_once __DIR__ . '/../vendor/autoload_runtime.php';

return static function (array &$context) {
    set_time_limit(0);

    $classLoader = require __DIR__ . '/../vendor/autoload.php';

    if (!class_exists(Application::class)) {
        throw new RuntimeException('You need to add "symfony/framework-bundle" as a Composer dependency.');
    }

    if (!isset($context['PROJECT_ROOT'])) {
        $context['PROJECT_ROOT'] = dirname(__DIR__);
    }

    $input = new ArgvInput();
    $env = $input->getParameterOption(['--env', '-e'], $context['APP_ENV'] ?? 'prod', true);
    $debug = ($context['APP_DEBUG'] ?? ($env !== 'prod')) && !$input->hasParameterOption('--no-debug', true);

    $pluginLoader = new StaticKernelPluginLoader($classLoader, null);

    if ($input->getFirstArgument() === 'system:install') {
        $context['INSTALL'] = true;
    }

    if (trim($context['DATABASE_URL'] ?? '') !== '' && !isset($context['INSTALL'])) {
        $pluginLoader = new DbalKernelPluginLoader($classLoader, null, Kernel::getConnection());
    }

    $kernel = KernelFactory::create(
        environment: $env,
        debug: $debug,
        classLoader: $classLoader,
        pluginLoader: $pluginLoader
    );

    $application = new Application($kernel);
    $kernel->boot();

    $application->setName('Shopware');
    $application->setVersion($kernel->getContainer()->getParameter('kernel.shopware_version'));

    return $application;
};
