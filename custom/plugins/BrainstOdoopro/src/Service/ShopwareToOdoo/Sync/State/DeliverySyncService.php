<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Sync\State;

use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\Model\Action;
use Brainst\OdooPro\Model\State;
use Brainst\OdooPro\Service\OdooMappingService;
use Brainst\OdooPro\Service\OdooService;
use Shopware\Core\Checkout\Order\Aggregate\OrderDelivery\OrderDeliveryEntity;
use Shopware\Core\Checkout\Order\Aggregate\OrderDelivery\OrderDeliveryStates;

/**
 * Class DeliverySyncService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class DeliverySyncService
{
    public function __construct(
        private readonly OdooService        $odooService,
        private readonly OdooMappingService $mappingService,
    )
    {
    }

    /**
     * Odoo order delivery manage
     *
     * @param OrderDeliveryEntity $delivery
     * @param array $odooRecord
     * @return void
     */
    public function update(OrderDeliveryEntity $delivery, array $odooRecord): void
    {
        $shopwareDeliveryStatus = $delivery->getStateMachineState()->getTechnicalName();
        $odooDeliveryAction = $this->getOdooDeliveryAction($shopwareDeliveryStatus);
        $deliveryIds = $odooRecord['picking_ids'];
        $companyId = $odooRecord['company_id'][0];

        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_DELIVERY, $delivery->getId());

        $odooId = $mapping?->getOdooId();
        // Record status if record exist in odoo then set
        $recStatus = "";
        if (in_array($odooId, $deliveryIds)) {
            $odooDelivery = $this->odooService->execute_kw('stock.picking', 'search_read',
                [[['id', '=', $odooId]], ['state']]);

            $recStatus = $odooDelivery[0]['state'];
        }
        $recState = State::load($recStatus);

        // Check and execute actions for delivery record
        if ((Action::create()->equals($odooDeliveryAction) && !State::assigned()->equals($recState)) || $recState->isEmpty()) {
            $odooId = $this->actionDeliveryCreate($odooRecord, $companyId);
        }
        if (Action::partialShip()->equals($odooDeliveryAction) || Action::partialReturn()->equals($odooDeliveryAction)) {
            if (State::done()->equals($recState)) {
                $this->actionDeliveryReturn($odooId, $companyId);
                $odooId = $this->actionDeliveryCreate($odooRecord, $companyId);
            }
        }

        if (Action::validate()->equals($odooDeliveryAction)) {
            $this->actionDeliveryValidate($odooId);
        }
        if (Action::return()->equals($odooDeliveryAction)) {
            if (($recState->isEmpty() || State::assigned()->equals($recState))) {
                $this->actionDeliveryValidate($odooId);
            }
            $this->actionDeliveryReturn($odooId, $companyId);
            $odooId = 0;
        }

        if (Action::cancel()->equals($odooDeliveryAction)) {
            if (State::done()->equals($recState)) {
                $this->actionDeliveryReturn($odooId, $companyId);
                $odooId = $this->actionDeliveryCreate($odooRecord, $companyId);
            }
            $this->odooService->execute_kw('stock.picking', 'action_cancel', [$odooId]);
        }

        $this->mappingService->updateMapping(BrainstOdooMudule::MODULE_DELIVERY, $delivery->getId(), $odooId, $mapping?->getId());
    }

    /**
     * Return delivery if not delivered otherwise return delivery
     *
     * @param OrderDeliveryEntity $delivery
     * @return void
     */
    public function delete(OrderDeliveryEntity $delivery): void
    {
        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_DELIVERY, $delivery->getId());
        if (!$mapping) {
            return;
        }
        $odooId = $mapping->getOdooId();
        // Fetch delivery record
        $odooDelivery = $this->odooService->execute_kw('stock.picking', 'search_read', [[['id', '=', $odooId]], ['state']]);

        if ($odooDelivery) {
            $deliveryStatus = $odooDelivery[0]['state'];

            // If delivery is done then change status for moved stock for deleting delivery
            if ($deliveryStatus === 'done') {
                $moveIds = $this->odooService->execute_kw('stock.move', 'search', [[['picking_id', '=', $odooId]]]);

                $this->odooService->execute_kw('stock.move', 'write',
                    $moveIds, ["state" => 'draft']);
            }
            // Delete delivery
            $this->odooService->execute_kw('stock.picking', 'unlink', [$odooId]);
            $this->mappingService->removeMapping($mapping->getId());
        }
    }

    /**
     * Create new delivery record for ship
     *
     * @param array $odooRecord
     * @param int $companyId
     * @return int
     */
    private function actionDeliveryCreate(array $odooRecord, int $companyId): int
    {
        // Create lines for delivery
        $odooOrderLineIds = $odooRecord['order_line'];
        $odooDeliveryLineItems = [];
        $saleProducts = [];

        $optionFields = ['product_id', 'name', 'product_uom_qty'];

        $lineItems = $this->odooService->execute_kw('sale.order.line', 'search_read',
            [[['id', 'in', $odooOrderLineIds]], $optionFields]);

        foreach ($lineItems as $lineItem) {
            $saleProducts[$lineItem['product_id'][0]] = $lineItem['id'];
            $fieldResult = [
                'product_id' => $lineItem['product_id'][0],
                'display_name' => $lineItem['name'],
                'origin' => $lineItem['id'],
                'quantity' => $lineItem['product_uom_qty']
            ];

            $odooDeliveryLineItems[] =
                [
                    0,
                    0,
                    $fieldResult
                ];
        }

        $deliveryData = [
            'partner_id' => $odooRecord['partner_shipping_id'][0],
            'sale_id' => $odooRecord['id'],
            'origin' => $odooRecord['name'],
            'move_type' => 'one',
            'move_line_ids' => $odooDeliveryLineItems,
        ];

        $deliveryType = $this->odooService->execute_kw('stock.picking.type', 'search',
            [[['company_id', '=', $companyId], ['code', '=', 'outgoing']]]);

        $createDeliveryData = $deliveryData;
        $createDeliveryData['picking_type_id'] = $deliveryType[0];
        $createDeliveryData['company_id'] = $companyId;

        $activeDeliveryId = $this->odooService->execute_kw('stock.picking', 'create', [$createDeliveryData]);

        // Sale line id
        $stockMoves = $this->odooService->execute_kw('stock.move', 'search_read',
            [
                [
                    ['picking_id', '=', $activeDeliveryId],
                    ['company_id', '=', $companyId]
                ],
                ['product_id']
            ]);
        foreach ($stockMoves as $stockMove) {
            $this->odooService->execute_kw('stock.move', 'write',
                [[$stockMove['id']], ["sale_line_id" => $saleProducts[$stockMove['product_id'][0]]]]);
        }
        $this->odooService->execute_kw('sale.order', 'write', [[$odooRecord['id']], ['picking_ids' => [[4, $activeDeliveryId]]]]);

        return $activeDeliveryId;
    }

    /**
     * Create new delivery record for return
     *
     * @param int $activeDeliveryId
     * @param int $companyId
     * @return void
     */
    private function actionDeliveryReturn(int $activeDeliveryId, int $companyId): void
    {
        // fetch moves for creating return delivery
        $stockMoves = $this->odooService->execute_kw('stock.move', 'search_read',
            [
                [
                    ['picking_id', '=', $activeDeliveryId],
                    ['company_id', '=', $companyId]
                ],
                ['product_id', 'quantity']
            ]);
        $moves = [];
        foreach ($stockMoves as $stockMove) {
            $moves[] = [
                0,
                0,
                [
                    'move_id' => $stockMove['id'],
                    'product_id' => $stockMove['product_id'][0],
                    'quantity' => $stockMove['quantity'],
                    'to_refund' => true,
                ]
            ];
        }

        // Create draft for return delivery
        $returnContext = [
            'active_id' => $activeDeliveryId,
            'active_ids' => [$activeDeliveryId],
            'active_model' => 'stock.picking',
            'allowed_company_ids' => [$companyId],
        ];
        $returnDataCreate = $this->odooService->execute_kw('stock.return.picking', 'web_save',
            [
                [],
                [
                    'picking_id' => $activeDeliveryId,
                    'product_return_moves' => $moves
                ]
            ], [
                'context' => $returnContext,
                'specification' => [
                    'picking_id' => ['fields' => []]
                ]
            ]);

        // Create return delivery
        $returnData = $this->odooService->execute_kw('stock.return.picking', 'create_returns',
            [$returnDataCreate[0]['id']],
            ['context' => $returnContext]
        );

        $activeReturnId = $returnData['res_id'];

        $this->actionDeliveryValidate($activeReturnId);
    }

    /**
     * Validate/Confirm the delivery record
     *
     * @param int $activeDeliveryId
     * @return void
     */
    private function actionDeliveryValidate(int $activeDeliveryId): void
    {
        $response = $this->odooService->execute_kw('stock.picking', 'button_validate', [$activeDeliveryId]);

        // When first time return data for sms confirmation pop-up
        if (is_array($response) && $response['res_id']) {
            $this->odooService->execute_kw('confirm.stock.sms', 'dont_send_sms', [$response['res_id']]);
            $this->odooService->execute_kw('stock.picking', 'button_validate', [$activeDeliveryId]);
        }
    }

    /**
     * Get odoo delivery action for update
     *
     * @param string $stateName
     * @return Action
     */
    private function getOdooDeliveryAction(string $stateName): Action
    {
        return match ($stateName) {
            OrderDeliveryStates::STATE_RETURNED => Action::return(),
            OrderDeliveryStates::STATE_PARTIALLY_SHIPPED => Action::partialShip(),
            OrderDeliveryStates::STATE_PARTIALLY_RETURNED => Action::partialReturn(),
            OrderDeliveryStates::STATE_SHIPPED => Action::validate(),
            OrderDeliveryStates::STATE_CANCELLED => Action::cancel(),
            default => Action::create(),
        };
    }
}
