<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Sync;

use Brainst\OdooPro\Service\ShopwareToOdoo\Sync\State\DeliverySyncService;
use Brainst\OdooPro\Service\ShopwareToOdoo\Sync\State\TransactionSyncService;
use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\Model\State;
use Brainst\OdooPro\Service\OdooMappingService;
use Brainst\OdooPro\Service\OdooService;
use Brainst\OdooPro\Service\ShopwareToOdoo\SyncHelper\CurrencyService;
use Brainst\OdooPro\Service\ShopwareToOdoo\SyncHelper\TaxService;
use Shopware\Core\Checkout\Order\Aggregate\OrderLineItem\OrderLineItemEntity;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Checkout\Order\OrderStates;
use Shopware\Core\Framework\Api\Context\SystemSource;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;

/**
 * Class OrderSyncService
 * @package BrainstOdoPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OrderSyncService implements SyncServiceInterface
{
    /**
     * @param OdooService $odooService
     * @param OdooMappingService $mappingService
     * @param EntityRepository<OrderCollection> $orderRepository
     * @param ProductSyncService $productService
     * @param CustomerSyncInterface $customerService
     * @param DeliverySyncService $deliverySyncService
     * @param TransactionSyncService $transactionSyncService
     * @param SyncServiceInterface $salesChannelSyncService
     */
    public function __construct(
        private readonly OdooService            $odooService,
        private readonly OdooMappingService     $mappingService,
        private readonly EntityRepository       $orderRepository,
        private readonly ProductSyncInterface   $productService,
        private readonly CustomerSyncInterface  $customerService,
        private readonly DeliverySyncService    $deliverySyncService,
        private readonly TransactionSyncService $transactionSyncService,
        private readonly SyncServiceInterface   $salesChannelSyncService
    )
    {
    }

    public function sync(string $recordId, string $operation, bool $initialSync = false, $statusType = null): void
    {
        $context = Context::createCLIContext();
        if ($operation === EntityWriteResult::OPERATION_DELETE) {
            $this->delete($recordId, $context);
            return;
        }
        if ($operation === 'status_update') {
            $this->orderStatusChange($recordId, $context, $statusType);
            return;
        }
        $this->update($recordId, $context);
    }

    public function update(string $recordId, Context $context): void
    {
        $orderData = $this->getOrder($recordId, $context);

        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_ORDER, $recordId);

        $pricelistId = $partnerId = $partnerInvoiceId = $partnerShippingId = 0;
        if (!$mapping) {
            if ($orderData->getOrderCustomer()->getCustomerId()) {
                $odooPartnerId = $this->customerService->getOrCreateOdooId($orderData->getOrderCustomer()->getCustomerId());
            } else {
                $odooPartnerId = $this->customerService->customCustomerId($orderData->getOrderCustomer());
            }

            $odooCompanyId = $this->salesChannelSyncService->getOrCreateOdooId($orderData->getSalesChannelId());
            $paymentTerms = $this->odooService->execute_kw('account.payment.term', 'name_search', []);
            $companySettings = $this->odooService->getConfig('companySettings', $orderData->getSalesChannelId());

            $paymentTermId = $paymentTerms[0][0];
            $odooLineItems = $this->getLineItems($orderData, $companySettings, $odooCompanyId);
            $pricelistId = CurrencyService::getOrUpdatePriceListId($this->odooService, $orderData->getCurrency(), $odooCompanyId);
        } else {
            $companySettings = $this->odooService->getConfig('companySettings', $orderData->getSalesChannelId());

            $odooRecs = $this->odooService->execute_kw('sale.order', 'search_read',
                [
                    [['id', '=', $mapping->getOdooId()]],
                    ['company_id', 'partner_id', 'payment_term_id', 'order_line', 'partner_invoice_id', 'partner_shipping_id']
                ]);
            $odooRecord = $odooRecs[0];
            $partnerInvoiceId = $odooRecord['partner_invoice_id'][0];
            $partnerShippingId = $odooRecord['partner_shipping_id'][0];

            $paymentTermId = $odooRecord['payment_term_id'][0];
            $odooCompanyId = $odooRecord['company_id'][0];
            $partnerId = $odooPartnerId = $odooRecord['partner_id'][0];
            $odooLineItems = $this->getLineItems($orderData, $companySettings, $odooCompanyId);
            $this->updateLineItems($odooRecord['order_line'], $odooLineItems);
        }

        $odooInvoicePartnerId = $this->customerService->getOdooAddressId($orderData->getBillingAddress(), $odooPartnerId);
        $odooShippingPartnerId = $this->customerService->getOdooAddressId($orderData->getDeliveries()->first()->getShippingOrderAddress(), $odooPartnerId);
        $createFields = [];
        if (!empty($odooLineItems)) {
            $createFields['order_line'] = array_values($odooLineItems);
        }
        if (!$mapping) {
            $createFields['pricelist_id'] = $pricelistId;
            $createFields['company_id'] = $odooCompanyId;
            $createFields['payment_term_id'] = $paymentTermId;
            $createFields['user_id'] = $this->odooService->getConfig('uid');
        }
        if ($odooPartnerId !== $partnerId) {
            $createFields['partner_id'] = $odooPartnerId;
        }
        if ($partnerInvoiceId !== $odooInvoicePartnerId) {
            $createFields['partner_invoice_id'] = $odooInvoicePartnerId;
        }
        if ($partnerShippingId !== $odooShippingPartnerId) {
            $createFields['partner_shipping_id'] = $odooShippingPartnerId;
        }
        if (!empty($createFields)) {
            $newOdooId = $this->upsertOdoo($createFields, $mapping?->getOdooId());
            $this->mappingService->updateMapping(BrainstOdooMudule::MODULE_ORDER, $recordId, $newOdooId, $mapping?->getId());
        }

        if (!$mapping) {
            $this->orderStatusChange($recordId, $context, 'all');
        }
    }

    public function delete(string $recordId, Context $context): void
    {
        $availableRecord = $this->orderRepository->search(new Criteria([$recordId]), $context)->first();
        if ($availableRecord) {
            return;
        }

        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_ORDER, $recordId);
        if ($mapping) {
            $this->odooService->execute_kw('sale.order', 'unlink', [[$mapping->getOdooId()]]);
            $this->mappingService->removeMapping($mapping->getId());
        }
    }

    public function getOrCreateOdooId(?string $recordId): ?int
    {
        if (!$recordId) {
            return null;
        }
        $odooId = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_ORDER, $recordId)?->getOdooId();
        if ($odooId) {
            return $odooId;
        }
        $this->sync($recordId, EntityWriteResult::OPERATION_INSERT);
        return $this->mappingService->findRecord(BrainstOdooMudule::MODULE_ORDER, $recordId)?->getOdooId();
    }

    public function orderStatusChange(string $recordId, Context $context, string $statusType): void
    {
        $orderData = $this->getOrder($recordId, $context);

        $mapping = $this->mappingService->findRecord(BrainstOdooMudule::MODULE_ORDER, $recordId);

        if (!$mapping) {
            $this->sync($recordId, EntityWriteResult::OPERATION_INSERT);
            return;
        }

        $odooRecs = $this->odooService->execute_kw('sale.order', 'search_read',
            [
                [['id', '=', $mapping->getOdooId()]],
                ['name', 'state', 'order_line', 'picking_ids', 'amount_to_invoice', 'delivery_status', 'partner_id', 'partner_invoice_id', 'partner_shipping_id', 'company_id', "currency_id"]
            ]);

        $stateName = $orderData->getStateMachineState()->getTechnicalName();

        $odooNewStatus = $this->getOdooOrderStatus($stateName);
        $odooRecord = $odooRecs[0];
        $odooStatus = $odooRecord['state'];

        if (!$odooNewStatus->equals(State::load($odooStatus))) {
            $this->upsertOdoo(['state' => $odooNewStatus->type()], $mapping->getOdooId());
        }
        if ($stateName === OrderStates::STATE_COMPLETED) {
            if ($statusType === 'delivery' || $statusType === 'all') {
                $delivery = $orderData->getDeliveries()->first();
                $this->deliverySyncService->update($delivery, $odooRecord);
            }
            if ($statusType === 'transaction' || $statusType === 'all') {
                $this->transactionSyncService->update($orderData, $odooRecord);
            }
        }
    }

    private function getOrder(string $recordId, Context $context): ?OrderEntity
    {
        $criteria = new Criteria([$recordId]);
        $criteria->addAssociations([
            'lineItems.product',
            'stateMachineState',
            'transactions',
            'transactions.stateMachineState',
            'deliveries',
            'deliveries.stateMachineState',
            'deliveries.shippingOrderAddress.country',
            'deliveries.shippingOrderAddress.countryState',
            'deliveries.shippingOrderAddress.salutation',
            'billingAddress',
            'billingAddress.country',
            'billingAddress.countryState',
            'billingAddress.salutation',
            'salesChannel',
            'currency'
        ]);

        return $this->orderRepository->search($criteria, $context)->first();
    }

    private function getLineItems(OrderEntity $orderData, array $companySettings, int $odooCompanyId): array
    {
        $odooLineItems = [];
        foreach ($orderData->getLineItems() as $lineItem) {
            $odooLineItems[] = $this->getOdooLine($lineItem, $orderData->getPrice()->getTaxStatus(), $companySettings, $odooCompanyId);
        }
        if ($orderData->getShippingTotal() > 0) {
            $odooLineItems[] = $this->getOdooShippingLineItem($orderData, $companySettings, $odooCompanyId);
        }
        return $odooLineItems;
    }

    private function updateLineItems(array $existingLineIds, &$odooLineItems): void
    {
        $lineItems = $this->odooService->execute_kw('sale.order.line', 'search_read',
            [[['id', 'in', $existingLineIds]], ['name', 'product_id', 'product_uom_qty', 'price_unit', 'price_total', 'tax_ids']]);
        $lineProductIds = [];
        foreach ($lineItems as $lineItem) {
            if (in_array($lineItem['product_id'][0], $lineProductIds)) {
                $odooLineItems[] = [2, $lineItem['id'], 0];
                continue;
            }

            $lineProductIds[] = $lineItem['product_id'][0];
            $key = $this->findProductIdKey($odooLineItems, $lineItem['product_id'][0]);
            if ($key !== null) {
                $newLineIteam = $odooLineItems[$key][2];
                if ($newLineIteam['name'] === $lineItem['name'] &&
                    $newLineIteam['product_id'] === $lineItem['product_id'][0] &&
                    $newLineIteam['product_uom_qty'] === $lineItem['product_uom_qty'] &&
                    $newLineIteam['price_unit'] === $lineItem['price_unit'] &&
                    $newLineIteam['price_total'] === $lineItem['price_total'] &&
                    ($newLineIteam['tax_ids'][0][1] ?? null) === ($lineItem['tax_ids'][0] ?? null)
                ) {
                    unset($odooLineItems[$key]);
                    continue;
                }
                $odooLineItems[$key] = [1, $lineItem['id'], $odooLineItems[$key][2]];
            } else {
                $odooLineItems[] = [2, $lineItem['id'], 0];
            }
        }
    }

    private function findProductIdKey(array $data, int $targetId): int|null
    {
        foreach ($data as $key => $item) {
            if (isset($item[2]['product_id']) && $item[2]['product_id'] === $targetId) {
                return $key;
            }
        }
        return null;
    }

    private function getOdooOrderStatus(string $stateName): State
    {
        return match ($stateName) {
            OrderStates::STATE_COMPLETED => State::sale(),
            OrderStates::STATE_CANCELLED => State::cancel(),
            default => State::draft(),
        };
    }

    private function getOdooLine(OrderLineItemEntity $lineItem, string $priceType, array $companySettings, int $companyId): array
    {
        $product = $lineItem->getProduct();
        $calculatedTaxes = $lineItem->getPrice()->getCalculatedTaxes();
        $unitPrice = $lineItem->getUnitPrice();
        $totalPrice = $lineItem->getTotalPrice();
        $quantity = (float)$lineItem->getQuantity();
        if ($priceType === 'net') {
            $totalPrice = $totalPrice + $calculatedTaxes->getAmount();
            $unitPrice = $totalPrice / $quantity;
        }

        $taxRate = $lineItem->getPrice()->getCalculatedTaxes()?->first()?->getTaxRate();
        if ($calculatedTaxes->count() > 1) {
            $odooTaxId = $this->getTaxId($totalPrice, $calculatedTaxes->getAmount(), $companyId, $companySettings);
        } else {
            $odooTaxId = TaxService::getOdooTaxId($this->odooService, $companyId, $taxRate, $companySettings);
        }

        if ($product && $product->getId()) {
            $odooProductId = $this->productService->getOrCreateOdooId($product->getId());
            $odooItemName = $product->getName() ?? $lineItem->getLabel();
        } else {
            $odooProductId = $this->productService->getOdooCustomProductId($lineItem->getLabel(), $unitPrice);
            $odooItemName = $lineItem->getLabel();
        }

        return $this->createLineIteam(
            $odooItemName,
            $odooProductId,
            $quantity,
            $unitPrice,
            $totalPrice,
            $odooTaxId);
    }

    /**
     * Create or Update the Odoo record
     *
     * @param array<string, null|int|string> $odooData
     * @param int|null $odooId
     * @return int
     */
    private function upsertOdoo(array $odooData, ?int $odooId): int
    {
        if ($odooId) {
            $this->odooService->execute_kw('sale.order', 'write', [[$odooId], $odooData]);
        } else {
            $lines = $odooData['order_line'];
            unset($odooData['order_line']);
            $odooId = $this->odooService->execute_kw('sale.order', 'create', [$odooData]);
            $this->odooService->execute_kw('sale.order', 'write', [[$odooId], ['order_line' => $lines]]);
        }
        return $odooId;
    }

    private function getOdooShippingLineItem(OrderEntity $orderEntity, array $companySettings, int $companyId): array
    {
        $priceType = $orderEntity->getPrice()->getTaxStatus();
        $lineItem = $orderEntity->getShippingCosts();
        $quantity = (float)$lineItem->getQuantity();
        $unitPrice = $lineItem->getUnitPrice();
        $totalPrice = $lineItem->getTotalPrice();
        $taxAmount = $lineItem->getCalculatedTaxes()->getAmount();
        if ($priceType === 'net') {
            $totalPrice = $totalPrice + $taxAmount;
            $unitPrice = $totalPrice / $quantity;
        }
        $odooTaxId = $this->getTaxId($totalPrice, $taxAmount, $companyId, $companySettings);

        $odooShippingId = $this->productService->getOdooCustomProductId('Shipping Charge', $unitPrice);
        return $this->createLineIteam(
            'Shipping Charge',
            $odooShippingId,
            $quantity,
            $unitPrice,
            $totalPrice,
            $odooTaxId);
    }

    private function createLineIteam(string $name, int $productId, float $quantity, float $price, float $priceTotal, int|false $odooTaxId): array
    {
        return [
            0,
            0,
            [
                'name' => $name,
                'product_id' => $productId,
                'product_uom_qty' => $quantity,
                'price_unit' => $price,
                'price_total' => $priceTotal,
                'tax_ids' => $odooTaxId ? [[4, $odooTaxId]] : false,
            ]
        ];
    }

    private function getTaxId($totalPrice, $taxAmount, $companyId, $companySettings): int|bool
    {
        $taxRate = ($taxAmount / ($totalPrice - $taxAmount)) * 100;
        if ($taxRate) {
            $taxRate = round($taxRate, 4);
            return TaxService::getOdooTaxId($this->odooService, $companyId, $taxRate, $companySettings);
        }
        return false;
    }
}
