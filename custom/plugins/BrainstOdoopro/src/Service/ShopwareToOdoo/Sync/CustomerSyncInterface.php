<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Sync;

use Shopware\Core\Checkout\Order\Aggregate\OrderAddress\OrderAddressEntity;
use Shopware\Core\Checkout\Order\Aggregate\OrderCustomer\OrderCustomerEntity;

/**
 * Class ProductSyncInterface
 * @package BrainstOdoPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface CustomerSyncInterface extends SyncServiceInterface
{
    public function getOdooAddressId(OrderAddressEntity $orderAddress, int $customerId): int;

    public function customCustomerId(OrderCustomerEntity $customer): int;
}