<?php declare(strict_types=1);

namespace Brainst\OdooPro\Service\ShopwareToOdoo\Queue;

use Brainst\OdooPro\MessageQueue\Message\OdooDataMessage;
use Brainst\OdooPro\Traits\MessageBusDispatchTrait;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Throwable;

/**
 * Class ProductSyncQueueService
 * @package BrainstOdooPro
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ProductSyncQueueService extends AbstractSyncQueue
{
    use MessageBusDispatchTrait;

    public final const LIMIT = 5;

    /**
     * @throws Throwable
     */
    public function __invoke(): void
    {
        $iterator = $this->getIterator(self::LIMIT);

        while ($searchResult = $iterator->fetch()) {
            $this->delayMessage(
                OdooDataMessage::init(
                    ProductDefinition::ENTITY_NAME,
                    $searchResult->getIds(),
                    EntityWriteResult::OPERATION_INSERT,
                    true)
            );
        }
    }

    protected function getCriteria(int $limit): Criteria
    {
        return parent::getCriteria($limit)
            ->addFilter(new EqualsFilter('parentId', null));
    }
}
