<?php

namespace Brainst\OdooPro\Service;

use Brainst\OdooPro\Core\Content\BrainstOdoo\BrainstOdooMudule;
use Brainst\OdooPro\MessageQueue\Message\FromOdooDataMessage;
use Brainst\OdooPro\Traits\MessageBusDispatchTrait;
use DateTime;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Doctrine\DBAL\Connection;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Throwable;

class OdooWebhookService
{
    use MessageBusDispatchTrait;
    public const SAME_REQUEST_RESTRICT_TIME = 6;
    public function __construct(
        private readonly OdooMappingService $mappingService,
        private readonly Connection         $connection,
        protected MessageBusInterface       $messageBus,
        protected LoggerInterface           $logger,
    )
    {
    }

    public function handleWebhook(array $payload, ?string $operation): void
    {
        try {
            if (!isset($payload['_model']) || !isset($payload['_id'])) {
                $this->logger->error('Invalid payload: ' . json_encode($payload));
                return;
            }

            if ($this->isInProcess($payload, $operation)) {
                $this->logger->notice('Frequently requests: ' . json_encode($payload));
                return;
            }

            $module = self::getModule($payload['_model']);
            if (!$module) {
                $this->logger->notice('Invalid module: ' . json_encode($payload));
                return;
            }

            if ($this->isDuplicateInQueue($module, $payload['_id'], $operation)) {
                $this->logger->notice('Already in queue: ' . json_encode($payload));
                return;
            }

            $mapping = $this->mappingService->findByOdooId($module, $payload['_id']);
            if ($mapping) {
                // Refatch and wait are for shopware generated record bounced from odoo before shopware complete process
                $mapping = $this->mappingService->findByOdooId($module, $payload['_id']);

                /** @var DateTime $updatedAt */
                $updatedAt = $mapping->getUpdatedAt() ?: $mapping->getCreatedAt();
                $delayTime = ($mapping->getSource() === 'shopware') ? '+2 minutes' : '+4 seconds';

                $expireTime = (clone $updatedAt)->modify($delayTime);
                if ($expireTime->getTimestamp() > (new DateTime())->getTimestamp()) {
                    $this->logger->alert('Potentially updated by shopware: ' . json_encode($payload));
                    return;
                }
                $this->delayMessage(FromOdooDataMessage::init($module, $payload['_id'], $operation), 10);
            }
            return;
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    private function isInProcess(array $payload, ?string $operation): bool
    {
        $cache = new FilesystemAdapter();
        $key = "processing_{$payload['_model']}_{$payload['_id']}_$operation";
        $item = $cache->getItem($key);
        if ($item->isHit()) {
            return true;
        }
        $item->set(true)->expiresAfter(self::SAME_REQUEST_RESTRICT_TIME);
        $cache->save($item);
        return false;
    }

    private function isDuplicateInQueue(string $module, int $id, ?string $operation): bool
    {
        $sql = $this->connection->createQueryBuilder();
        $count = $sql->select('COUNT(*)')
            ->from('messenger_messages')
            ->where('body = :body')
            ->andWhere('queue_name != "failed"')
            ->andWhere('delivered_at IS NULL')
            ->setParameter('body', json_encode(['id' => $id, 'model' => $module, 'operation' => $operation]))
            ->fetchOne();
        return (bool)$count;
    }

    public static function getModule(string $odooModel): ?string
    {
        if ($odooModel === 'product.category') {
            return BrainstOdooMudule::MODULE_CATEGORY;
        }
        if ($odooModel === 'product.template') {
            return BrainstOdooMudule::MODULE_TEMPLATE;
        }
        if ($odooModel === 'res.partner') {
            return BrainstOdooMudule::MODULE_CUSTOMER;
        }
        if ($odooModel === 'product.attribute') {
            return BrainstOdooMudule::MODULE_ATTRIBUTE;
        }
        return null;
    }
} 