<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>General configuration</title>
        <title lang="de-DE">Allgemeine Konfiguration</title>

        <input-field type="bool">
            <name>enableIntegration</name>
            <label>Enable the integration</label>
            <label lang="de-DE">Aktivieren Sie die Integration</label>
            <helpText>Activate the integration with Odoo and send data to Odoo.</helpText>
            <helpText lang="de-DE">Aktivieren Sie die Integration mit Odoo und senden Sie Daten an Odoo.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>
        <input-field type="bool">
            <name>initialSync</name>
            <label>Enable the initial sync by GUI</label>
            <label lang="de-DE">Aktivieren Sie die anfängliche Synchronisierung per GUI</label>
            <helpText>Activate the initial sync with Odoo and do the sync by button in extension settings. Please be cautious while enabling it if you have done the initial sync, If you have done sync and tried multiple times then the queue will get busy for a long time again.</helpText>
            <helpText lang="de-DE">Aktivieren Sie die anfängliche Synchronisierung mit Odoo und führen Sie die Synchronisierung über die Schaltfläche in den Erweiterungseinstellungen durch. Bitte seien Sie beim Aktivieren vorsichtig, wenn Sie die erste Synchronisierung durchgeführt haben. Wenn Sie die Synchronisierung durchgeführt und es mehrmals versucht haben, wird die Warteschlange erneut für eine lange Zeit beschäftigt sein.</helpText>
            <defaultValue>true</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Odoo configuration</title>
        <title lang="de-DE">Odoo Konfiguration</title>

        <input-field type="url">
            <name>serverUrl</name>
            <label>Odoo server URL</label>
            <label lang="de-DE">Odoo-Server-URL</label>
            <helpText>Odoo server URL.</helpText>
            <helpText lang="de-DE">Odoo-Server-URL.</helpText>
            <defaultValue/>
        </input-field>
        <input-field type="text">
            <name>username</name>
            <label>Odoo username</label>
            <label lang="de-DE">Odoo-Benutzername</label>
            <helpText>Odoo username of API user.</helpText>
            <helpText lang="de-DE">Odoo-Benutzername des API-Benutzers.</helpText>
            <defaultValue/>
        </input-field>
        <input-field type="password">
            <name>password</name>
            <label>Odoo API key/Password</label>
            <label lang="de-DE">Odoo API-Schlüssel/Passwort</label>
            <helpText>Odoo API key of the user, or optionally you can also use the password.</helpText>
            <helpText lang="de-DE">Odoo API-Schlüssel des Benutzers, optional können Sie auch das Passwort verwenden.</helpText>
            <defaultValue/>
        </input-field>
        <input-field type="text">
            <name>database</name>
            <label>Odoo database name</label>
            <label lang="de-DE">Name der Odoo-Datenbank</label>
            <helpText>Odoo database name.</helpText>
            <helpText lang="de-DE">Name der Odoo-Datenbank.</helpText>
            <defaultValue/>
        </input-field>

        <component name="brainst-odoo-pro-api-verify-button">
            <name>odooApiVerify</name>
        </component>

        <input-field type="text">
            <name>version</name>
            <label>Odoo Version</label>
            <label lang="de-DE"></label>
            <helpText>Version of the odoo instance.</helpText>
            <helpText lang="de-DE"></helpText>
            <defaultValue/>
            <disabled>true</disabled>
        </input-field>

        <input-field type="text">
            <name>uid</name>
            <label>Odoo UID</label>
            <label lang="de-DE"></label>
            <helpText>UID of the odoo user.</helpText>
            <helpText lang="de-DE"></helpText>
            <defaultValue/>
            <disabled>true</disabled>
        </input-field>
    </card>
</config>
