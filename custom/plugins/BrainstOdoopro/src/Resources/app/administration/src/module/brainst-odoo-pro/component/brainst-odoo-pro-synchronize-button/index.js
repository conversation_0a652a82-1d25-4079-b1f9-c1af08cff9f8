const {Component, Mixin} = Shopware;

import template from './brainst-odoo-pro-synchronize-button.html.twig';
import './brainst-odoo-pro-synchronize-button.scss';

Component.register('brainst-odoo-pro-synchronize-button', {
    template,

    inject: [
        'brainstOdooProSynchronizationService'
    ],

    mixins: [
        Mixin.getByName('notification'),
    ],

    data() {
        return {
            isLoading: false,
            isVerifyDone: false,
            showConfirmation: false,
        };
    },

    methods: {
        showConfirmationModal() {
            this.showConfirmation = true;
        },

        hideConfirmationModal() {
            this.showConfirmation = false;
        },

        confirmSynchronization() {
            this.hideConfirmationModal();
            this.synchronize();
        },

        synchronize() {
            this.isLoading = true;
            this.brainstOdooProSynchronizationService
                .synchronise()
                .then((response) => {
                    if (response.code) {
                        this.createNotificationError({
                            title: this.$tc('brainst-odoo-pro.synchronize-button.title'),
                            message: this.$tc('brainst-odoo-pro.synchronize-button.error_' + response.code)
                        })
                    } else {
                        this.createNotificationSuccess({
                            title: this.$tc('brainst-odoo-pro.synchronize-button.title'),
                            message: this.$tc('brainst-odoo-pro.synchronize-button.success')
                        });
                    }
                })
                .catch((exception) => {
                    let code = 2;
                    if (exception.response && exception.response.code) {
                        code = exception.response.code;
                    }
                    this.createNotificationError({
                        title: this.$tc('brainst-odoo-pro.synchronize-button.title'),
                        message: this.$tc('brainst-odoo-pro.synchronize-button.error_' + code)
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    }
});