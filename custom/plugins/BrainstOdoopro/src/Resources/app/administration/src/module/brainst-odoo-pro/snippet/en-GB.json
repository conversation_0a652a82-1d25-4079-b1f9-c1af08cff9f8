{"brainst-odoo-pro": {"title": "Brainst Odoo Pro", "description": "Shopware Odoo synchronization data of categories, sales channels, delivery, property, products, customers, and orders.", "list": {"title": "Brainst Odoo Pro", "sales-channel": "Sales Channel Table", "category": "Category Table", "product": "Product Table", "property": "Property Table", "attribute-value": "Property Value Table", "customer": "Customer Table", "customer-address": "Customer Address Table", "order": "Order Table", "delivery": "Delivery Table", "transaction": "Transaction Table"}, "category": {"title": "Category Table", "table": {"id": "Shopware Category Id", "odoo-id": "Odoo Category Id", "name": "Category Name"}}, "product": {"title": "Product Table", "tabs": {"template": "Products", "product": "Variants"}, "notes": {"template": "These are main products which are saved as templates in Odoo.", "product": "This includes all variants from products and products which do not have variants. Odoo creates a variant for each product. Badge indicators: P = Parent Product, V = Variant Product."}, "badges": {"parent": "Parent Product", "variant": "Variant Product"}, "table": {"id": "Shopware Product Id", "odoo-id": "Odoo Product Id", "name": "Product Name"}, "labels": {"id": "Product variant identifier in Shopware", "odoo-id": "Corresponding product variant ID in Odoo system", "name": "Display name with variant options (P = Parent, V = Variant)"}}, "template": {"title": "Template Table", "table": {"id": "Shopware Product Id", "odoo-id": "Odoo Template Id", "name": "Product Name"}, "labels": {"id": "Main product identifier in Shopware", "odoo-id": "Corresponding template ID in Odoo system", "name": "Display name of the main product"}}, "property": {"title": "Property Table", "label": "Property", "note": "Shopware properties are synchronized in Odoo as attributes.", "table": {"id": "Shopware Property Id", "odoo-id": "Odoo Attribute Id", "name": "Property Name"}}, "customer": {"title": "Customer Table", "table": {"id": "Shopware Customer Id", "odoo-id": "Odoo Customer Id", "name": "Customer Name"}}, "order": {"title": "Order Table", "table": {"id": "Shopware Order Id", "odoo-id": "Odoo Order Id", "name": "Order Number"}}, "sales-channel": {"title": "Sales Channel Table", "table": {"id": "Shopware Sales Channel Id", "odoo-id": "Odoo Company Id", "name": "Sales Channel Name"}}, "attribute-value": {"title": "Property Value Table", "label": "Property Value", "note": "Shopware property values are synchronized in Odoo as attribute values.", "table": {"id": "Shopware Property Value Id", "odoo-id": "Odoo Attribute Value Id", "name": "Property Value Name"}}, "customer-address": {"title": "Customer Address Table", "label": "Customer Address", "note": "Customer addresses are synchronized as child contacts in Odoo, with the Shopware customer serving as the parent contact.", "table": {"id": "Shopware Address Id", "odoo-id": "Odoo Contact Id", "name": "Address Name"}}, "delivery": {"title": "Delivery Table", "table": {"id": "Shopware Delivery Id", "odoo-id": "Odoo Delivery Id", "name": "Order Number"}}, "transaction": {"title": "Transaction Table", "table": {"id": "Shopware Transaction Id", "odoo-id": "Odoo Payment Id", "name": "Order Number"}}, "api-verify-button": {"title": "API Test", "buttonLabel": "Verify API Connection", "success": "Connection tested with success", "error": "Connection could not be established. Please check the access data", "error_400": "Something went wrong. Please check the access data", "error_401": "Authentication failed please check cradintials.", "error_422": "This version of odoo is not supported, supports only odoo 17 and 18.", "error_424": "Some modules are not installed, please install", "error_500": "Server error"}, "synchronize-button": {"title": "Sync", "description": "This will take some time to process the queue, so please do it when your site usage is minimal for some time.<br/>The estimated time is 1000 products per Hour*.", "success": "All entities are queued for synchronization", "error_1": "Please setup the odoo configuration, then try again", "error_2": "Something went wrong with the synchronization, please try again", "error_3": "You already have done or in the process of initial syncing", "confirmation": {"title": "Confirm Synchronization", "message": "Are you sure you want to start the synchronization process? Once started, this action cannot be undone and will synchronize all data between Shopware and Odoo.", "warning": "This process may take considerable time and should be performed during low traffic periods.", "confirm": "Yes, Start Synchronization", "cancel": "Cancel"}}, "progressBar": {"text": "Synchronization Progress", "idle": "Ready", "syncing": "Syncing", "completed": "Completed", "error": "Error", "remaining": "remaining"}, "automation-modal": {"title": "Automation Configuration", "description": "Configure automation settings for two-way synchronization between Shopware and Odoo.", "toggleLabel": "Enable Two-Way Sync", "toggleHelpText": "When enabled, changes in Odoo will automatically sync back to Shopware", "saveButton": "Save", "success": "Automation configuration saved successfully", "error": "Failed to create automation rules. Please check your Odoo connection and try again."}, "dashboard": {"refresh": "Refresh", "title": "Synchronization Dashboard", "totalSynced": "Total Synced", "entityTypes": "Entity Types", "entitySynced": "<PERSON><PERSON><PERSON> Synced", "syncStatus": "Sync Status", "overallProgress": "Overall Progress", "synced": "Synced", "pending": "Pending", "total": "Total", "records": "records", "gauge": {"title": "Sync Rate", "recordsPerHour": "Records/Hour", "recordsPerMinute": "Records/Minute", "hour": "hour", "minute": "minute", "lastHour": "Last Hour", "lastMinute": "Last Minute", "switchToHour": "Switch to Hour View", "switchToMinute": "Switch to Minute View"}, "entityNames": {"salesChannels": "Sales Channels", "categories": "Categories", "productVariants": "Product Variants", "products": "Products", "propertyGroups": "Property Groups", "propertyGroupOptions": "Property Group Options", "customers": "Customers", "customerAddresses": "Customer Addresses", "orders": "Orders", "deliveries": "Deliveries", "transactions": "Transactions"}, "tooltips": {"complete": "All records are synchronized", "partial": "Some records are synchronized", "pending": "No records are synchronized yet", "noData": "No data available"}}}}