<div>
    <sw-button-process
            variant="primary"
            :isLoading="isLoading"
            :processSuccess="isVerifyDone"
            @click="showConfirmationModal">
        <sw-icon :small="true" name="regular-sync"></sw-icon>
        {{ $tc('brainst-odoo-pro.synchronize-button.title') }}
    </sw-button-process>
    <sw-help-text :text="$tc('brainst-odoo-pro.synchronize-button.description')" />

    <!-- Confirmation Modal -->
    <sw-modal
        v-if="showConfirmation"
        class="brainst-odoo-pro-sync-confirmation-modal"
        :title="$tc('brainst-odoo-pro.synchronize-button.confirmation.title')"
        variant="small"
        @modal-close="hideConfirmationModal"
    >
        <div class="brainst-odoo-pro-sync-confirmation-modal__content">
            <div class="brainst-odoo-pro-sync-confirmation-modal__message">
                <sw-icon name="regular-exclamation-triangle" size="24px" class="warning-icon"></sw-icon>
                <p class="confirmation-text">
                    {{ $tc('brainst-odoo-pro.synchronize-button.confirmation.message') }}
                </p>
                <p class="warning-text">
                    {{ $tc('brainst-odoo-pro.synchronize-button.confirmation.warning') }}
                </p>
            </div>
        </div>

        <template #modal-footer>
            <sw-button
                size="small"
                @click="hideConfirmationModal"
                :disabled="isLoading"
            >
                {{ $tc('brainst-odoo-pro.synchronize-button.confirmation.cancel') }}
            </sw-button>
            <sw-button-process
                class="brainst-odoo-pro-sync-confirmation-modal__confirm-button"
                :isLoading="isLoading"
                variant="primary"
                size="small"
                @click="confirmSynchronization"
            >
                {{ $tc('brainst-odoo-pro.synchronize-button.confirmation.confirm') }}
            </sw-button-process>
        </template>
    </sw-modal>
</div>