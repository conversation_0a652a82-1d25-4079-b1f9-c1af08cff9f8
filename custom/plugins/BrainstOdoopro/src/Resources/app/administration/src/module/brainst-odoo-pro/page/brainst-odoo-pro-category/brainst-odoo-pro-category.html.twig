<sw-page>
    <template #smart-bar-back>
        {% block sw_page_slot_smart_bar_back %}
            <brainst-odoo-pro-back-button />
        {% endblock %}
    </template>

    <template #smart-bar-header>
        {% block brainst_odoo_table_category_smart_bar_header_title %}
            <h2>{{ $tc('brainst-odoo-pro.category.title') }}</h2>
        {% endblock %}
    </template>

    <template #content>
        {% block brainst_odoo_table_category_list_content %}
            <brainst-odoo-pro-progress-bar entity="category" :value="total"/>
            <sw-entity-listing
                    v-if="brainstOdooEntries"
                    :items="brainstOdooEntries"
                    :repository="brainstOdooRepository"
                    :showSelection="false"
                    :columns="columns"
                    :allowDelete="false">
                <template #column-recordId="{ item, column }">
                    <router-link
                            class="sw-data-grid__cell-value"
                            :to="{ name: 'sw.category.detail', params: { id: item.recordId } }"
                    >
                        {{ item.category.name }}
                    </router-link>
                </template>
                <template #column-odooId="{ item, column }">
                    <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.category`"
                       class="sw-data-grid__cell-value" rel="nofollow noopener"
                       target="_blank">{{ item.odooId }}</a>
                </template>
            </sw-entity-listing>
        {% endblock %}
    </template>
</sw-page>
