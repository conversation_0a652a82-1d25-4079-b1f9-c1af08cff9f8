<sw-page>
    <template #smart-bar-back>
        {% block sw_page_slot_smart_bar_back %}
            <brainst-odoo-pro-back-button />
        {% endblock %}
    </template>

    <template #smart-bar-header>
        {% block brainst_odoo_table_product_smart_bar_header_title %}
            <h2>{{ $tc('brainst-odoo-pro.product.title') }}</h2>
        {% endblock %}
    </template>

    <template #content>
        {% block brainst_odoo_table_product_list_content %}
            <!-- Tab Navigation -->
            <div class="sw-tabs">
                <div class="sw-tabs__header">
                    <button
                        class="sw-tabs__item"
                        :class="{ 'sw-tabs__item--active': activeTab === 'template' }"
                        @click="setActiveTab('template')"
                    >
                        <sw-icon name="regular-products" size="16"></sw-icon>
                        {{ $tc('brainst-odoo-pro.product.tabs.template') }}
                        <span class="sw-tabs__item-badge" v-if="templateTotal > 0">{{ templateTotal }}</span>
                    </button>
                    <button
                        class="sw-tabs__item"
                        :class="{ 'sw-tabs__item--active': activeTab === 'product' }"
                        @click="setActiveTab('product')"
                    >
                        <sw-icon name="regular-file-text" size="16"></sw-icon>
                        {{ $tc('brainst-odoo-pro.product.tabs.product') }}
                        <span class="sw-tabs__item-badge" v-if="total > 0">{{ total }}</span>
                    </button>
                </div>
            </div>

            <!-- Product Tab Content -->
            <div v-if="activeTab === 'product'" class="tab-content">
                <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">
                    <div class="sw-alert__message">
                        <strong>{{ $tc('brainst-odoo-pro.product.tabs.product') }}:</strong> {{ $tc('brainst-odoo-pro.product.notes.product') }}
                    </div>
                </sw-alert>
                <brainst-odoo-pro-progress-bar entity="product" module="product" :value="total"/>
                <sw-entity-listing
                        v-if="brainstOdooEntries"
                        :items="brainstOdooEntries"
                        :repository="brainstOdooRepository"
                        :showSelection="false"
                        :columns="columns"
                        :allowDelete="false"
                        @update-records="recordUpdate($event)">
                    <template #column-recordId="{ item, column }">
                        <router-link
                                class="sw-data-grid__cell-value"
                                :to="{ name: 'sw.product.detail', params: { id: item.recordId } }"
                        >
                            <div class="product-name-with-badge">
                                <span v-if="item.product?.name || !item.product?.parentId">
                                    {{ item.product?.name }}
                                </span>
                                <span v-else>
                                    {{ item.parentName }}
                                    (<template v-for="(value, key) in item.product?.options">
                                        {{ value.group.name }}: {{ value.name }}{{ item?.product?.options.length - 1 > key ? " | " : "" }}
                                    </template>)
                                </span>
                                <span
                                    class="product-type-badge"
                                    :class="{ 'badge-parent': !item.product?.parentId, 'badge-variant': item.product?.parentId }"
                                    :title="!item.product?.parentId ? $tc('brainst-odoo-pro.product.badges.parent') : $tc('brainst-odoo-pro.product.badges.variant')"
                                >
                                    {{ !item.product?.parentId ? 'P' : 'V' }}
                                </span>
                            </div>
                        </router-link>
                    </template>
                    <template #column-odooId="{ item, column }">
                        <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.product`"
                           class="sw-data-grid__cell-value" rel="nofollow noopener"
                           target="_blank">{{ item.odooId }}</a>
                    </template>
                </sw-entity-listing>
            </div>

            <!-- Template Tab Content -->
            <div v-if="activeTab === 'template'" class="tab-content">
                <sw-alert variant="info" class="sw-alert--no-icon" style="margin-bottom: 20px;">
                    <div class="sw-alert__message">
                        <strong>{{ $tc('brainst-odoo-pro.product.tabs.template') }}:</strong> {{ $tc('brainst-odoo-pro.product.notes.template') }}
                    </div>
                </sw-alert>
                <brainst-odoo-pro-progress-bar entity="product" module="template" :value="templateTotal"/>
                <sw-entity-listing
                        v-if="templateEntries"
                        :items="templateEntries"
                        :repository="brainstOdooRepository"
                        :showSelection="false"
                        :columns="templateColumns"
                        :allowDelete="false">
                    <template #column-recordId="{ item, column }">
                        <router-link
                                class="sw-data-grid__cell-value"
                                :to="{ name: 'sw.product.detail', params: { id: item.recordId } }"
                        >
                            {{ item.product ? item.product.name : item.recordId }}
                        </router-link>
                    </template>
                    <template #column-odooId="{ item, column }">
                        <a :href="`${odooUrl}/web#id=${item.odooId}&view_type=form&model=product.template`"
                           class="sw-data-grid__cell-value" rel="nofollow noopener"
                           target="_blank">{{ item.odooId }}</a>
                    </template>
                </sw-entity-listing>
            </div>
        {% endblock %}
    </template>
</sw-page>
