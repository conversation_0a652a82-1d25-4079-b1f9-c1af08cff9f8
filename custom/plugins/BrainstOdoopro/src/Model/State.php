<?php

declare(strict_types=1);

namespace Brainst\OdooPro\Model;

/**
 * Class State
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
final class State implements ModelInterface
{
    private const ODOO_STATE_DRAFT = 'draft';
    private const ODOO_STATE_CANCEL = 'cancel';
    private const ODOO_STATE_SALE = 'sale';
    private const ODOO_STATE_POSTED = 'posted';
    private const ODOO_STATE_ASSIGNED = 'assigned';
    private const ODOO_STATE_DONE = 'done';

    private function __construct(private readonly string $type)
    {
    }
    public function type(): string
    {
        return $this->type;
    }

    public function equals(ModelInterface $model): bool
    {
        return $model->type() === $this->type;
    }

    public static function load(string $type): self
    {
        return new self($type);
    }

    public static function draft(): self
    {
        return new self(self::ODOO_STATE_DRAFT);
    }

    public static function cancel(): self
    {
        return new self(self::ODOO_STATE_CANCEL);
    }

    public static function sale(): self
    {
        return new self(self::ODOO_STATE_SALE);
    }

    public static function posted(): self
    {
        return new self(self::ODOO_STATE_POSTED);
    }

    public static function assigned(): self
    {
        return new self(self::ODOO_STATE_ASSIGNED);
    }

    public static function done(): self
    {
        return new self(self::ODOO_STATE_DONE);
    }

    public static function empty(): self
    {
        return new self("");
    }

    public function isEmpty(): bool
    {
        return $this->type === "";
    }
}
