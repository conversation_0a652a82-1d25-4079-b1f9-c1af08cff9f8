<?php declare(strict_types=1);

namespace Flink\SizeCharts\Core\Content\SizeChart\Aggregate\SizeChartProductTranslation;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void                           add(SizeChartProductTranslationEntity $entity)
 * @method void                           set(string $key, SizeChartProductTranslationEntity $entity)
 * @method SizeChartProductTranslationEntity[]    getIterator()
 * @method SizeChartProductTranslationEntity[]    getElements()
 * @method SizeChartProductTranslationEntity|null get(string $key)
 * @method SizeChartProductTranslationEntity|null first()
 * @method SizeChartProductTranslationEntity|null last()
 */
class SizeChartProductTranslationCollection extends EntityCollection
{
    public function getApiAlias(): string
    {
        return 'flink_size_chart_product_translation_collection';
    }

    protected function getExpectedClass(): string
    {
        return SizeChartProductTranslationEntity::class;
    }
}
