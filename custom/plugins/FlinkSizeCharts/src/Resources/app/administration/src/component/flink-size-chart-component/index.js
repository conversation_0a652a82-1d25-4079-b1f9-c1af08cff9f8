import template from "./flink-size-chart-component.html.twig";
import "./flink-size-chart-component.scss";
import Papa from "papaparse";

const { Component } = Shopware;

Component.register('flink-size-chart-component', {
    template,

    props: {
        // item is the flinkSizeChart
        item: {
            type: Object,
            required: true,
        },
        // show or hide buttons
        fixed: {
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            currentTabIdx: 0,
            itemDetailModalOpen: false,
            currentInputItem: {row: null, col: null},
        }
    },

    computed: {

        charts() {
            const raw = this.item.chartsOverride;
            const charts = JSON.parse(JSON.stringify(raw));

            charts.forEach((tab) => {
                tab.cols = tab.cols.map((c, idx) => {
                    return {
                        idx: idx,
                        property: `col-${idx}`,
                        label: c
                    }
                });
                tab.rows = tab.rows.map((r, ridx) => {
                    const row = {id: ridx};
                    r.forEach((c, cidx) => {
                        row[`col-${cidx}`] = c;
                    });
                    return row;
                });
            });
            return charts;
        },

        currentTab() {
            return this.charts[this.currentTabIdx];
        },

        chartTabs() {
            return this.charts.map((item) => {
                return item.tab;
            });
        },

        chartColumns() {
            return this.currentTab.cols;
        },

        chartRows() {
            return this.currentTab.rows;
        },

        currentInputItemHtml() {
            try {
                const tab = this.item.chartsOverride[this.currentTabIdx];
                const row = this.currentInputItem.row;
                const col = this.currentInputItem.col;
                return tab.rows[row][col];
            } catch {
                return "";
            }
        },
    },

    methods: {
        onAddChartTab() {
            this.item.chartsOverride.push({
                tab: "[name]",
                cols: ['',''],
                rows: [['','']],
                links: []
            });
        },

        onDeleteChartTab(idx) {
            if (idx === this.currentTabIdx && typeof this.item.chartsOverride[idx+1] == 'undefined') {
                this.currentTabIdx--
            }
            this.item.chartsOverride.splice(idx, 1);
        },

        onAddChartRow() {
            this.item.chartsOverride[this.currentTabIdx].rows.push(
                Array(this.chartColumns.length).fill('')
            );
        },

        onDeleteChartRow(idx) {
            this.item.chartsOverride[this.currentTabIdx].rows.splice(idx, 1);
        },

        onAddChartColumn() {
            this.item.chartsOverride[this.currentTabIdx].cols.push('');
            this.item.chartsOverride[this.currentTabIdx].rows.forEach((row) => {
                row.push('');
            });
        },

        onDeleteChartColumn(idx) {
            this.item.chartsOverride[this.currentTabIdx].cols.splice(idx, 1);
            this.item.chartsOverride[this.currentTabIdx].rows.forEach((row) => {
                row.splice(idx, 1);
            });
        },

        onInputChartField(value, row, col) {
            this.$set(this.item.chartsOverride[this.currentTabIdx].rows[row], col, value);
        },

        onInputChartLabel(value, col) {
            this.$set(this.item.chartsOverride[this.currentTabIdx].cols, col, value);
        },

        onOpenItemDetailModal(event, rowIdx, colIdx) {
            this.currentInputItem.row = rowIdx;
            this.currentInputItem.col = colIdx;
            this.itemDetailModalOpen = true;
        },

        onCloseItemDetailModal() {
            this.currentInputItem.row = null;
            this.currentInputItem.col = null;
            this.itemDetailModalOpen = false;
        },

        onInputItemHtml(value) {
            const row = this.currentInputItem.row;
            const col = this.currentInputItem.col;
            this.$set(this.item.chartsOverride[this.currentTabIdx].rows[row], col, value);
        },

        containsHTML(string) {
            return !!string.length && /<\/?[a-z][\s\S]*>/i.test(string);
        },

        convertHtmlForInput(str) {
            const element = document.createElement('div');
            if(str && typeof str === 'string') {
                // strip script/html tags
                str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                element.innerHTML = str;
                str = element.textContent;
                element.textContent = '';
            }
            return str;
        },

        onClickImportCsv() {
            this.$refs.csvInput.click();
        },

        onImportCsv() {
            const that = this;
            Papa.parse(this.$refs.csvInput.files[0], {
                header: false,
                skipEmptyLines: false,
                complete: function (results, file) {
                    console.log("NOTICE: Parsing complete", results, file);
                    const newChartData = [];
                    const data = results.data;
                    let tabIdx = 0;
                    let tableRow = 0;
                    for (let idx in data) {

                        if (!data.hasOwnProperty(idx)) {
                            continue;
                        }

                        let row = data[idx];
                        if (idx == 0 || !row.filter(that.emptyFilter).length) {
                            newChartData.push({
                                tab: "",
                                cols: [],
                                rows: []
                            });
                            tabIdx = newChartData.length - 1;
                            tableRow = 0;
                        }

                        if (row.filter(that.emptyFilter).length) {
                            if (tableRow === 0) {
                                newChartData[tabIdx].tab = row[0];
                            } else if (tableRow === 1) {
                                newChartData[tabIdx].cols = row;
                            } else {
                                newChartData[tabIdx].rows.push(row);
                            }
                            tableRow++;
                        }

                    }

                    for (let tabIdx in newChartData) {
                        if (newChartData.hasOwnProperty(tabIdx)) {
                            tabIdx = parseInt(tabIdx, 10);
                            if (newChartData[tabIdx].rows.length === 0) {
                                newChartData.splice(tabIdx, 1);
                            }
                        }
                    }

                    that.$set(that.item, 'charts', newChartData);
                    that.$refs.csvForm.reset();
                }
            });
        },

        emptyFilter(item) {
            return !!item;
        },

        onChartTabMoveUp(idx) {
            this.arrayMove(this.item.chartsOverride, idx, idx -1);
        },

        onChartTabMoveDown(idx) {
            this.arrayMove(this.item.chartsOverride, idx, idx + 1);
        },

        onSortChartTabs() {
            this.item.chartsOverride.sort((x, y) => {
                if (x.tab < y.tab) {
                    return -1;
                }
                if (x.tab > y.tab) {
                    return 1;
                }
                return 0;
            })
        },

        arrayMove(arr, old_index, new_index) {
            if (new_index >= arr.length) {
                var k = new_index - arr.length + 1;
                while (k--) {
                    arr.push(undefined);
                }
            }
            arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
        },
    }
});
