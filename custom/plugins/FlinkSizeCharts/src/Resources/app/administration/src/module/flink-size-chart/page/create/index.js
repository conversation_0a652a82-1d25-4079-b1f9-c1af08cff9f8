const { Component } = Shopware;

Component.extend('flink-size-chart-create', 'flink-size-chart-detail', {
    methods: {
        getItem() {
            this.item = this.repository.create(Shopware.Context.api);
            this.isLoading = false;
            this.item.charts = [];
            this.onAddChartTab();
        },
        onClickSave() {
            this.isLoading = true;

            this.saveAttempt = true;

            if (this.anyError) {
                this.isLoading = false;
                this.createNotificationError({
                    title: this.$t('flink-size-chart.notification.errorTitle'),
                    message: this.$t('flink-size-chart.notification.errorText')
                });

                return;
            }

            this.repository
                .save(this.item, Shopware.Context.api)
                .then(() => {
                    this.isLoading = false;
                    this.saveAttempt = false;
                    this.$router.push({name: 'flink.size.chart.detail', params: {id: this.item.id}});
                }).catch((exception) => {
                this.isLoading = false;
                this.createNotificationError({
                    title: this.$t('flink-size-chart.notification.errorTitle'),
                    message: this.$t('flink-size-chart.notification.errorText')
                });
            });
        }
    }
});
