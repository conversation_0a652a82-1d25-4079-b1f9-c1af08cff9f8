<?php declare(strict_types=1);

namespace Brainst\OfferBell;

use Brainst\OfferBell\Service\OfferBellMediaFolderService;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Kernel;

class BrainstOfferBell extends Plugin
{
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $this->getOfferbellMediaService()->createMediaFolder($installContext->getContext());
    }

    /**
     * @throws Exception
     */
    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }

        $this->getOfferbellMediaService()->deleteMediaFolder($uninstallContext->getContext());
        $this->getOfferbellMediaService()->deleteDefaultMediaFolder($uninstallContext->getContext());

        $connection = Kernel::getConnection();
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_offer_translation`');
        $connection->executeStatement('DROP TABLE IF EXISTS `brainst_offer`');
    }

    private function getOfferbellMediaService(): OfferBellMediaFolderService
    {
        if ($this->container->has(OfferBellMediaFolderService::class)) {
            return $this->container->get(OfferBellMediaFolderService::class);
        }

        return new OfferbellMediaFolderService(
            $this->container->get('media_default_folder.repository'),
            $this->container->get('media_folder.repository'),
            $this->container->get('media_thumbnail_size.repository'),
        );
    }
}