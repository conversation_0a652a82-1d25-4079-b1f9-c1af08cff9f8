<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>

        <service id="Brainst\OfferBell\Service\OfferBellMediaFolderService" public="true">
            <argument type="service" id="media_default_folder.repository"/>
            <argument type="service" id="media_folder.repository"/>
            <argument type="service" id="media_thumbnail_size.repository"/>
        </service>

        <service id="Brainst\OfferBell\Core\Content\BrainstOffer\BrainstOfferDefinition">
            <tag name="shopware.entity.definition" entity="brainst_offer"/>
        </service>

        <service
                id="Brainst\OfferBell\Core\Content\BrainstOffer\Aggregate\BrainstOfferTranslation\BrainstOfferTranslationDefinition">
            <tag name="shopware.entity.definition" entity="brainst_offer_translation"/>
        </service>

    </services>
</container>