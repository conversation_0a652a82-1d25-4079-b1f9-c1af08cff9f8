(window["webpackJsonpPluginswag-extension-store"]=window["webpackJsonpPluginswag-extension-store"]||[]).push([[476],{562:function(){},476:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return a}}),n(340);let s="SwagAnalytics";var a=Shopware.Component.wrapComponentConfig({template:'<div\n    v-if="showBanner"\n    class="sw-dashboard-statistics-promotion-banner"\n>\n    <div class="sw-dashboard-statistics-promotion-banner__advertisement">\n        <div class="sw-dashboard-statistics-promotion-banner__advertisement-details">\n            <div\n                v-if="showBadge"\n                class="sw-dashboard-statistics-promotion-banner__advertisement-details-badge"\n            >\n                {{ $tc(\'badge-new\') }}\n            </div>\n            <h3 class="sw-dashboard-statistics-promotion-banner__advertisement-details-title">\n                {{ $tc(\'promotion-title\') }}\n            </h3>\n\n            <div class="sw-dashboard-statistics-promotion-banner__advertisement-details-description">\n                {{ $tc(\'promotion-text\') }}\n            </div>\n        </div>\n        <div class="sw-dashboard-statistics-promotion-banner__advertisement-graph">\n            <img :src="assetFilter(\'/swagextensionstore/static/img/analytics/promotion/graphic.svg\')" />\n        </div>\n    </div>\n\n    <div class="sw-dashboard-statistics-promotion-banner__app-card">\n        <div class="sw-dashboard-statistics-promotion-banner__app-card-app">\n            <sw-extension-icon\n                class="sw-dashboard-statistics-promotion-banner__app-card-app-icon"\n                :src="assetFilter(\'/swagextensionstore/static/img/analytics/extension/icon.svg\')"\n            />\n\n            <div class="sw-dashboard-statistics-promotion-banner__app-card-app-info">\n                <h4 class="sw-dashboard-statistics-promotion-banner__app-card-app-info-name">\n                    {{ $tc(\'app-name\') }}\n                </h4>\n\n                <span class="sw-dashboard-statistics-promotion-banner__app-card-app-info-description">\n                    {{ $tc(\'app-description\') }}\n                </span>\n            </div>\n        </div>\n\n        <sw-button\n            class="sw-dashboard-statistics-promotion-banner__app-card-go-to-app"\n            variant="primary"\n            :disabled="!linkToStatisticsAppExists"\n            @click="goToStatisticsAppDetailPage"\n        >\n            {{ $tc(\'go-to-app\') }} <sw-icon name="regular-long-arrow-right" size="12px" />\n        </sw-button>\n    </div>\n</div>\n',inject:["extensionStoreDataService","acl"],i18n:{messages:{"en-GB":{"badge-new":"Available now","promotion-title":"Shopware Analytics","promotion-text":"Discover the new suite of KPIs with your sales and performance metrics in Shopware. Get ready to benefit from an ever-expanding range of fresh insights to support your journey toward success!","app-name":"Shopware Analytics","app-description":"Unlock store performance metrics","go-to-app":"Try it out now"},"de-DE":{"badge-new":"Jetzt verf\xfcgbar","promotion-title":"Shopware Analytics","promotion-text":"Entdecke die neue Suite an Kennzahlen mit Deinen Verkaufs- und Leistungsmetriken in Shopware. Mach Dich bereit, von einer st\xe4ndig wachsenden Auswahl an Auswertungen zu profitieren, die Deine Reise zum Erfolg unterst\xfctzen!","app-name":"Shopware Analytics","app-description":"Erfasse wichtige Shop-Kennzahlen","go-to-app":"Jetzt ausprobieren"}}},data(){return{isAppInstalled:!1,routeToApp:null}},computed:{showBanner(){return!this.isAppInstalled},showBadge(){return new Date<new Date("2025-01-01 00:00:00.000")},linkToStatisticsAppExists(){return!!this.routeToApp},assetFilter(){return Shopware.Filter.getByName("asset")}},created(){this.createdComponent()},methods:{async createdComponent(){if(this.isAppInstalled=!!Shopware.Context.app.config.bundles[s],!this.canAccessExtensionStore()){this.routeToApp={name:"sw.extension.store"};return}this.extensionStoreDataService.getExtensionByName(s,Shopware.Context.api).then(e=>{e&&(this.routeToApp={name:"sw.extension.store.detail",params:{id:e.id}})})},goToStatisticsAppDetailPage(){this.linkToStatisticsAppExists&&this.$router.push(this.routeToApp)},canAccessExtensionStore(){return this.acl.can("system.plugin_maintain")}}})},340:function(e,t,n){var s=n(562);s.__esModule&&(s=s.default),"string"==typeof s&&(s=[[e.id,s,""]]),s.locals&&(e.exports=s.locals),n(346).Z("9ccb70ea",s,!0,{})},346:function(e,t,n){"use strict";function s(e,t){for(var n=[],s={},a=0;a<t.length;a++){var i=t[a],r=i[0],o={id:e+":"+a,css:i[1],media:i[2],sourceMap:i[3]};s[r]?s[r].parts.push(o):n.push(s[r]={id:r,parts:[o]})}return n}n.d(t,{Z:function(){return m}});var a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},r=a&&(document.head||document.getElementsByTagName("head")[0]),o=null,p=0,d=!1,c=function(){},l=null,u="data-vue-ssr-id",h="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(e,t,n,a){d=n,l=a||{};var r=s(e,t);return f(r),function(t){for(var n=[],a=0;a<r.length;a++){var o=i[r[a].id];o.refs--,n.push(o)}t?f(r=s(e,t)):r=[];for(var a=0;a<n.length;a++){var o=n[a];if(0===o.refs){for(var p=0;p<o.parts.length;p++)o.parts[p]();delete i[o.id]}}}}function f(e){for(var t=0;t<e.length;t++){var n=e[t],s=i[n.id];if(s){s.refs++;for(var a=0;a<s.parts.length;a++)s.parts[a](n.parts[a]);for(;a<n.parts.length;a++)s.parts.push(g(n.parts[a]));s.parts.length>n.parts.length&&(s.parts.length=n.parts.length)}else{for(var r=[],a=0;a<n.parts.length;a++)r.push(g(n.parts[a]));i[n.id]={id:n.id,refs:1,parts:r}}}}function v(){var e=document.createElement("style");return e.type="text/css",r.appendChild(e),e}function g(e){var t,n,s=document.querySelector("style["+u+'~="'+e.id+'"]');if(s){if(d)return c;s.parentNode.removeChild(s)}if(h){var a=p++;t=b.bind(null,s=o||(o=v()),a,!1),n=b.bind(null,s,a,!0)}else t=y.bind(null,s=v()),n=function(){s.parentNode.removeChild(s)};return t(e),function(s){s?(s.css!==e.css||s.media!==e.media||s.sourceMap!==e.sourceMap)&&t(e=s):n()}}var w=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function b(e,t,n,s){var a=n?"":s.css;if(e.styleSheet)e.styleSheet.cssText=w(t,a);else{var i=document.createTextNode(a),r=e.childNodes;r[t]&&e.removeChild(r[t]),r.length?e.insertBefore(i,r[t]):e.appendChild(i)}}function y(e,t){var n=t.css,s=t.media,a=t.sourceMap;if(s&&e.setAttribute("media",s),l.ssrId&&e.setAttribute(u,t.id),a&&(n+="\n/*# sourceURL="+a.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}}}]);