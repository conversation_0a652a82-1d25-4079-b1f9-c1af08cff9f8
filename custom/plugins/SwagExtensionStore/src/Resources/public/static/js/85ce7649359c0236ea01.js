(window["webpackJsonpPluginswag-extension-store"]=window["webpackJsonpPluginswag-extension-store"]||[]).push([[564],{601:function(){},564:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return s}}),t(273);var s={template:'{% block sw_extension_store_index %}\n    <sw-meteor-page class="sw-extension-store-index" hideIcon>\n        {% block sw_extension_store_index_slots %}\n            {% block sw_extension_store_index_slot_search_bar %}\n                <template #search-bar>\n                    {% block sw_extension_store_index_search_bar %}\n                        <sw-search-bar\n                            :key="storeSearchKey"\n                            @search="updateSearch"\n                            :initialSearch="searchValue"\n                            initialSearchType="extension"\n                            :placeholder="$tc(\'sw-extension-store.listing.placeholderSearchBar\')"\n                        />\n                    {% endblock %}\n                </template>\n            {% endblock %}\n\n            {% block sw_extension_store_index_slot_tabs %}\n                <template #page-tabs>\n                    {% block sw_extension_store_index_tabs %}\n                        {% block sw_extension_store_index_tabs_extensions_app %}\n                            <sw-tabs-item :route="{ name: \'sw.extension.store.listing.app\' }" :disabled="!isAvailable">\n                                {{ $tc(\'sw-extension-store.tabs.apps\') }}\n                            </sw-tabs-item>\n                        {% endblock %}\n\n                        {% block sw_extension_store_index_tabs_extensions_theme %}\n                            <sw-tabs-item :route="{ name: \'sw.extension.store.listing.theme\' }" :disabled="!isAvailable">\n                                {{ $tc(\'sw-extension-store.tabs.themes\') }}\n                            </sw-tabs-item>\n                        {% endblock %}\n                    {% endblock %}\n                </template>\n            {% endblock %}\n\n            {% block sw_extension_store_index_slot_default %}\n                <template #default>\n\n                    {% block sw_extension_store_index_loader %}\n                        <sw-loader v-if="isLoading"></sw-loader>\n                    {% endblock %}\n\n                    <template v-else>\n                        {% block sw_extension_store_index_content %}\n                            {% block sw_extension_store_index_content_view %}\n                                <router-view\n                                    v-if="isAvailable"\n                                    @extension-listing-errors="onExtensionListingError">\n                                </router-view>\n                            {% endblock %}\n\n                            {% block sw_extension_store_index_content_offline_warning %}\n                                <sw-extension-store-error-card\n                                    v-else-if="failReason === \'offline\'"\n                                    :title="$tc(\'sw-extension-store.offline.headline\')"\n                                    variant="danger"\n                                >\n                                    {{ $tc(\'sw-extension-store.offline.description\') }}\n                                </sw-extension-store-error-card>\n                            {% endblock %}\n\n                            {% block sw_extension_store_index_content_update_warning %}\n                                <sw-extension-store-update-warning v-else-if="failReason === \'outdated\'">\n                                </sw-extension-store-update-warning>\n                            {% endblock %}\n\n                            {% block sw_extension_store_index_content_listing_error %}\n                                <sw-extension-store-error-card\n                                    v-else\n                                    :title="listingError && listingError.title"\n                                    variant="danger"\n                                >\n                                    <template v-if="listingError">\n                                        {{ listingError.message }}\n                                    </template>\n                                </sw-extension-store-error-card>\n                            {% endblock %}\n                        {% endblock %}\n                    </template>\n                </template>\n            {% endblock %}\n        {% endblock %}\n    </sw-meteor-page>\n{% endblock %}\n',inject:["extensionStoreActionService","shopwareExtensionService","feature"],props:{id:{type:String,required:!1,default:null}},data(){return{isAvailable:!1,failReason:"",listingError:null,isLoading:!1}},computed:{storeSearchKey(){return this.$route.name},activeFilters(){return Shopware.State.get("shopwareExtensions").search.filter},searchValue(){return Shopware.State.get("shopwareExtensions").search.term},isTheme(){return this.$route.name.includes("theme")?"themes":"apps"}},watch:{isTheme:{immediate:!0,handler(e){Shopware.State.commit("shopwareExtensions/setSearchValue",{key:"page",value:1}),this.activeFilters.group=e}}},created(){this.createdComponent()},methods:{createdComponent(){this.checkStoreUpdates()},async checkStoreUpdates(){this.isLoading=!0,this.shopwareExtensionService.updateExtensionData();let e=await this.getExtensionStore();if(!e){this.isLoading=!1;return}if(this.isUpdateable(e)){this.isAvailable=!1,this.failReason="outdated",this.isLoading=!1;return}this.isAvailable=!0,this.isLoading=!1},onExtensionListingError(e){let n=Shopware.Service("extensionErrorService").handleErrorResponse(e,this);this.isAvailable=!1,this.listingError=n&&n[0],this.failReason="listing_error"},getExtensionStore(){return this.extensionStoreActionService.getMyExtensions().then(e=>e.find(e=>"SwagExtensionStore"===e.name))},isUpdateable(e){return!!e&&null!==e.latestVersion&&e.latestVersion!==e.version},updateSearch(e){Shopware.State.commit("shopwareExtensions/setSearchValue",{key:"term",value:e})}}}},273:function(e,n,t){var s=t(601);s.__esModule&&(s=s.default),"string"==typeof s&&(s=[[e.id,s,""]]),s.locals&&(e.exports=s.locals),t(346).Z("b17e179e",s,!0,{})},346:function(e,n,t){"use strict";function s(e,n){for(var t=[],s={},r=0;r<n.length;r++){var i=n[r],o=i[0],a={id:e+":"+r,css:i[1],media:i[2],sourceMap:i[3]};s[o]?s[o].parts.push(a):t.push(s[o]={id:o,parts:[a]})}return t}t.d(n,{Z:function(){return f}});var r="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!r)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=r&&(document.head||document.getElementsByTagName("head")[0]),a=null,l=0,d=!1,c=function(){},u=null,p="data-vue-ssr-id",h="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,n,t,r){d=t,u=r||{};var o=s(e,n);return _(o),function(n){for(var t=[],r=0;r<o.length;r++){var a=i[o[r].id];a.refs--,t.push(a)}n?_(o=s(e,n)):o=[];for(var r=0;r<t.length;r++){var a=t[r];if(0===a.refs){for(var l=0;l<a.parts.length;l++)a.parts[l]();delete i[a.id]}}}}function _(e){for(var n=0;n<e.length;n++){var t=e[n],s=i[t.id];if(s){s.refs++;for(var r=0;r<s.parts.length;r++)s.parts[r](t.parts[r]);for(;r<t.parts.length;r++)s.parts.push(b(t.parts[r]));s.parts.length>t.parts.length&&(s.parts.length=t.parts.length)}else{for(var o=[],r=0;r<t.parts.length;r++)o.push(b(t.parts[r]));i[t.id]={id:t.id,refs:1,parts:o}}}}function x(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function b(e){var n,t,s=document.querySelector("style["+p+'~="'+e.id+'"]');if(s){if(d)return c;s.parentNode.removeChild(s)}if(h){var r=l++;n=g.bind(null,s=a||(a=x()),r,!1),t=g.bind(null,s,r,!0)}else n=m.bind(null,s=x()),t=function(){s.parentNode.removeChild(s)};return n(e),function(s){s?(s.css!==e.css||s.media!==e.media||s.sourceMap!==e.sourceMap)&&n(e=s):t()}}var w=function(){var e=[];return function(n,t){return e[n]=t,e.filter(Boolean).join("\n")}}();function g(e,n,t,s){var r=t?"":s.css;if(e.styleSheet)e.styleSheet.cssText=w(n,r);else{var i=document.createTextNode(r),o=e.childNodes;o[n]&&e.removeChild(o[n]),o.length?e.insertBefore(i,o[n]):e.appendChild(i)}}function m(e,n){var t=n.css,s=n.media,r=n.sourceMap;if(s&&e.setAttribute("media",s),u.ssrId&&e.setAttribute(p,n.id),r&&(t+="\n/*# sourceURL="+r.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}}}]);