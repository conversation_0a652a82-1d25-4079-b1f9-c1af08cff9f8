<?php declare(strict_types=1);

namespace Brainst\Xero\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
class Migration1733993886BrainstXero extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1733993886;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $sql = <<<SQL
                CREATE TABLE IF NOT EXISTS `brainst_xero` (
                    `id` BINARY(16) NOT NULL,
                    `record_id` BINARY(16) NOT NULL,
                    `xero_id` VARCHAR(36) NOT NULL,
                    `module` VARCHAR(20) NOT NULL,
                    `sales_channel_id` BINARY(16) DEFAULT NULL,
                    `created_at` DATETIME(3) NOT NULL,
                    `updated_at` DATETIME(3),
                    PRIMARY KEY (`id`),
                    INDEX `idx_module_sales_channel_id` (`module`, `sales_channel_id`),
                    INDEX `idx_record_module_sales_channel_id` (`record_id`, `module`, `sales_channel_id`)
                )
                ENGINE = InnoDB
                DEFAULT CHARSET = utf8mb4
                COLLATE = utf8mb4_unicode_ci;
            SQL;
        $connection->executeStatement($sql);
    }
}
