<?php declare(strict_types=1);

namespace Brainst\Xero\Service\Sync;

use Brainst\Xero\Model\Operation;

/**
 * Class SyncServiceInterface
 * @package BrainstXero
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface SyncServiceInterface
{
    /**
     * Update, insert or delete based on provided operation
     *
     * @param string $entityId
     * @param Operation $operation
     * @param bool $initialSync
     * @return string[]
     */
    public function sync(string $entityId, Operation $operation, bool $initialSync = false): array;
}