<?php

declare(strict_types=1);

namespace Brainst\Odoo\Subscriber;

use Brainst\Odoo\Model\Operation;
use Brainst\Odoo\Service\ChunkDispatch\CustomerDispatchService;
use Brainst\Odoo\Service\Synchronisation\CustomerSynchronisationService;
use Shopware\Core\Checkout\Customer\CustomerEvents;
use Shopware\Core\Checkout\Customer\CustomerDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityDeletedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class ListenToCustomerChanges
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ListenToCustomerChanges implements EventSubscriberInterface
{
    public function __construct(
        protected RequestStack                          $requestStack,
        private readonly CustomerSynchronisationService $customerSynchronisationService,
        private readonly CustomerDispatchService        $customerDispatchService
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CustomerEvents::CUSTOMER_WRITTEN_EVENT => 'OnCustomerWritten',
            CustomerEvents::CUSTOMER_DELETED_EVENT => 'onCustomerDelete',
        ];
    }


    /**
     * Add or update customer in Odoo when customer added or updated
     *
     * @param EntityWrittenEvent $event
     * @return void
     */
    public function OnCustomerWritten(EntityWrittenEvent $event): void
    {
        $attributes = $this->requestStack->getCurrentRequest()->attributes;
        $route = $attributes->get('_route');
        if ($route !== 'api.customer.delete' && $route !== 'api.customer.update' && $route !== 'api.customer.create') {
            return;
        }

        $customerIds = [];
        foreach ($event->getWriteResults() as $writeResult) {
            if ($writeResult->getOperation() === EntityWriteResult::OPERATION_INSERT || $writeResult->getOperation() === EntityWriteResult::OPERATION_UPDATE) {
                $customerIds[] = $writeResult->getPrimaryKey();
            }
        }
        if (!empty($customerIds)) {
            ($this->customerDispatchService)($customerIds, Operation::update());
        }
    }

    /**
     * Delete customer from Odoo
     *
     * @param EntityDeletedEvent $event
     * @return void
     */
    public function onCustomerDelete(EntityDeletedEvent $event): void
    {
        $customerIds = [];
        $deletedIds = [];
        foreach ($event->getWriteResults() as $writeResult) {
            if ($writeResult->getEntityName() !== CustomerDefinition::ENTITY_NAME) {
                continue;
            }
            $deleteId = $this->customerSynchronisationService->getOdooRecordId($writeResult->getPrimaryKey(),
                $event->getContext());
            $deletedIds[$writeResult->getPrimaryKey()] = $deleteId;
            $customerIds[] = $writeResult->getPrimaryKey();
        }
        if (!empty($customerIds)) {
            ($this->customerDispatchService)($customerIds, Operation::delete(), $deletedIds);
        }
    }
}