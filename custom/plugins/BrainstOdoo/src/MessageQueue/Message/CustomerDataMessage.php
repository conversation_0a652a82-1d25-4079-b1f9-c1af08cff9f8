<?php
declare(strict_types=1);

namespace Brainst\Odoo\MessageQueue\Message;

use Shopware\Core\Framework\MessageQueue\AsyncMessageInterface;

/**
 * Class CustomerDataMessage
 * @package BrainstOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CustomerDataMessage implements AsyncMessageInterface
{
    /**
     * Property added for checking event for log entry
     *
     * @var string
     */
    public string $messageType = 'odoo_sync_message';

    public function __construct(
        private readonly array  $customerIds,
        private readonly string $operation,
        private readonly array  $odooCustomerIds,
    )
    {
    }

    /**
     * Create Object statically
     * @param array $customerIds
     * @param string $operation
     * @param array $odooCustomerIds
     * @return CustomerDataMessage
     */
    public static function forCustomerIdsWithOperation(
        array  $customerIds,
        string $operation,
        array  $odooCustomerIds = [],
    ): CustomerDataMessage
    {
        return new self($customerIds, $operation, $odooCustomerIds);
    }

    /**
     * @return array
     */
    public function getCustomerIds(): array
    {
        return $this->customerIds;
    }

    /**
     * @return array
     */
    public function getOdooCustomerIds(): array
    {
        return $this->odooCustomerIds;
    }

    /**
     * @return string
     */
    public function getOperation(): string
    {
        return $this->operation;
    }
}