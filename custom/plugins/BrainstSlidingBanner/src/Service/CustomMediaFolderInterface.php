<?php

declare(strict_types=1);

namespace Brainst\SlidingBanner\Service;

use Shopware\Core\Framework\Context;

/**
 * Class CustomMediaFolderInterface
 * @package BrainstSlidingBanner
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
interface CustomMediaFolderInterface
{
    /**
     * We need to create a folder for the banner media with it's,
     * own configuration to generate thumbnails for the banners.
     *
     * @param Context $context
     * @return void
     */
    public function createMediaFolder(Context $context): void;

    /**
     * Delete default media folder if already exist
     * @param Context $context
     * @return void
     */
    public function deleteDefaultMediaFolder(Context $context): void;

    /**
     * Delete custom media folder
     *
     * @param Context $context
     * @return void
     */
    public function deleteMediaFolder(Context $context): void;
}