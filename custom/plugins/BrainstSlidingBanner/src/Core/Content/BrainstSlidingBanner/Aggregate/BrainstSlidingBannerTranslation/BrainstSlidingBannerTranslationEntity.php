<?php declare(strict_types=1);

namespace Brainst\SlidingBanner\Core\Content\BrainstSlidingBanner\Aggregate\BrainstSlidingBannerTranslation;

use Brainst\SlidingBanner\Core\Content\BrainstSlidingBanner\BrainstSlidingBannerEntity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCustomFieldsTrait;
use Shopware\Core\Framework\DataAbstractionLayer\TranslationEntity;

/**
 * Class BrainstSlidingBannerTranslationEntity
 * @package BrainstSlidingBanner
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BrainstSlidingBannerTranslationEntity extends TranslationEntity
{
    use EntityCustomFieldsTrait;

    protected string $link;
    protected string $title;
    protected ?string $description;
    protected ?string $label;
    protected string $brainstSlidingBannerId;
    protected BrainstSlidingBannerEntity $brainstSlidingBanner;


    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getLink(): string
    {
        return $this->link;
    }

    public function setLink(string $link): void
    {
        $this->link = $link;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): void
    {
        $this->label = $label;
    }

    public function getBrainstSlidingBannerId(): string
    {
        return $this->brainstSlidingBannerId;
    }

    public function setBrainstSlidingBannerId(string $brainstSlidingBannerId): void
    {
        $this->brainstSlidingBannerId = $brainstSlidingBannerId;
    }

    public function getBrainstSlidingBanner(): BrainstSlidingBannerEntity
    {
        return $this->brainstSlidingBanner;
    }

    public function setBrainstSlidingBanner(BrainstSlidingBannerEntity $brainstSlidingBanner): void
    {
        $this->brainstSlidingBanner = $brainstSlidingBanner;
    }
}
