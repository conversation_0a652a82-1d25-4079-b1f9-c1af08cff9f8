<?php declare(strict_types=1);

namespace Brainst\AutofillCity\Migration;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1719313120CreateBrainstPostalCodeTable extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1719313120;
    }

    /**
     * @throws Exception
     */
    public function update(Connection $connection): void
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `brainst_postal_code` (
    `id` BINARY(16) NOT NULL,
    `country_id` BINARY(16) NOT NULL,
    `code` BIGINT  NOT NULL,
    `city` VARCHAR(255)  NOT NULL,
    `active` TINYINT(1) DEFAULT 1 COMMENT '0=Inactive, 1=Active',
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3),
    PRIMARY KEY (`id`),
    CONSTRAINT `fk.brainst_postal_code.country_id` FOREIGN KEY (`country_id`)
        REFERENCES `country` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;
SQL;

        $connection->executeStatement($sql);
    }
}
