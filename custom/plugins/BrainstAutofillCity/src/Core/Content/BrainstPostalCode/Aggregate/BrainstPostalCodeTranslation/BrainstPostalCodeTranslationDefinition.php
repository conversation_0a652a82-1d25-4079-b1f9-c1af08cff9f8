<?php declare(strict_types=1);

namespace Brainst\AutofillCity\Core\Content\BrainstPostalCode\Aggregate\BrainstPostalCodeTranslation;

use Brainst\AutofillCity\Core\Content\BrainstPostalCode\BrainstPostalCodeDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityTranslationDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ApiAware;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;


class BrainstPostalCodeTranslationDefinition extends EntityTranslationDefinition
{
    public const ENTITY_NAME = 'brainst_postal_code_translation';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return BrainstPostalCodeTranslationEntity::class;
    }

    public function getCollectionClass(): string
    {
        return BrainstPostalCodeTranslationCollection::class;
    }

    public function getParentDefinitionClass(): string
    {
        return BrainstPostalCodeDefinition::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new StringField('city', 'city'))->addFlags(new ApiAware()),
        ]);
    }
}