<?php declare(strict_types=1);

namespace Brainst\AutofillCity\Core\Content\BrainstPostalCode\Aggregate\BrainstPostalCodeTranslation;

use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;

/**
 * @method void                             add(BrainstPostalCodeTranslationEntity $entity)
 * @method void                             set(string $key, BrainstPostalCodeTranslationEntity $entity)
 * @method BrainstPostalCodeTranslationEntity[]    getIterator()
 * @method BrainstPostalCodeTranslationEntity[]    getElements()
 * @method BrainstPostalCodeTranslationEntity|null get(string $key)
 * @method BrainstPostalCodeTranslationEntity|null first()
 * @method BrainstPostalCodeTranslationEntity|null last()
 */
class BrainstPostalCodeTranslationCollection extends EntityCollection
{
    protected function getExpectedClass(): string
    {
        return BrainstPostalCodeTranslationEntity::class;
    }
}