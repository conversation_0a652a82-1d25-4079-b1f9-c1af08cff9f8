<?php declare(strict_types=1);

namespace Brainst\AutofillCity\Core\Content\BrainstPostalCode\SalesChannel;

use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

abstract class AbstractBrainstPostalCodeRoute
{
    /**
     * @return AbstractBrainstPostalCodeRoute
     */
    abstract public function getDecorated(): AbstractBrainstPostalCodeRoute;

    /**
     * @param Criteria $criteria
     * @param SalesChannelContext $context
     * @return BrainstPostalCodeRouteResponse
     */
    abstract public function load(Criteria $criteria, SalesChannelContext $context): BrainstPostalCodeRouteResponse;

    /**
     * @param Criteria $criteria
     * @param SalesChannelContext $context
     * @param string $postalCode
     * @param string|null $countryId
     * @return BrainstPostalCodeRouteResponse
     */
    abstract public function getCity(Criteria $criteria, SalesChannelContext $context, string $postalCode, ?string $countryId = null): BrainstPostalCodeRouteResponse;
}