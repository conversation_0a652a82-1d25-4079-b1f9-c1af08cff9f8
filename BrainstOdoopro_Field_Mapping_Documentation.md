# BrainstOdoopro Plugin - Odoo Field Mapping Documentation

## Overview
This document provides a comprehensive mapping of Shopware fields to Odoo fields for the BrainstOdoopro plugin. The plugin synchronizes data between Shopware 6 and Odoo across multiple modules.

## Module Hierarchy

### Core Modules
1. **Sales Channel** (`sales_channel`)
2. **Category** (`category`) 
3. **Product** (`product`)
4. **Template** (`template`)
5. **Customer** (`customer`)
6. **Customer Address** (`customer_address`)
7. **Property/Attribute** (`attribute`)
8. **Property Value** (`attribute_value`)
9. **Order** (`order`)
10. **Delivery** (`delivery`)
11. **Transaction** (`transaction`)

---

## 1. Sales Channel Module (`sales_channel`)

**Odoo Model:** `res.company`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Sales channel name |
| `countryId` | `country_id` | Default country for the sales channel |
| `currencyId` | `currency_id` | Default currency for the sales channel |

**Custom Values Added:**
- Unique name generation to avoid conflicts in Odoo
- Company settings synchronization

---

## 2. Category Module (`category`)

**Odoo Model:** `product.category`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Category name |
| `parentId` | `parent_id` | Parent category reference |

**Custom Values Added:**
- Hierarchical category structure maintained
- Automatic parent-child relationship mapping

---

## 3. Product Module (`product`)

**Odoo Model:** `product.template` (for main products) / `product.product` (for variants)

### Main Product Fields
| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Product name |
| `price.gross` | `list_price` | Product selling price |
| `description` | `description` | Product description |
| `categoryIds[0]` | `categ_id` | Primary category assignment |
| `productNumber` | `default_code` | Product SKU/code (for variants) |

**Custom Values Added:**
- Price conversion from Shopware pricing structure
- Category mapping through CategorySyncService
- Variant handling with attribute line IDs
- Stock quantity synchronization

### Product Variants
- Variants are handled separately with `default_code` mapping
- Parent products get full field mapping
- Variant products only get product number mapping

---

## 4. Template Module (`template`)

**Odoo Model:** `product.template`

Used for Odoo-to-Shopware synchronization of product templates.

| Odoo Field | Shopware Field | Description |
|------------|----------------|-------------|
| `name` | `name` | Product template name |
| `description` | `description` | Product description |
| `list_price` | `price.net` | Net price (converted to gross) |
| `qty_available` | `stock` | Available quantity |

**Custom Values Added:**
- Auto-generated product number for new products
- Tax calculation for gross price conversion
- Default stock value of 0 for new products
- Active status set to true

---

## 5. Customer Module (`customer`)

**Odoo Model:** `res.partner`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `firstName + lastName` | `name` | Full customer name |
| `email` | `email` | Customer email address |
| `title` | `function` | Customer title/position |
| `vatIds` | `vat` | VAT identification number |
| `defaultBillingAddress.street` | `street` | Primary street address |
| `defaultBillingAddress.additionalAddressLine1` | `street2` | Additional address line |
| `defaultBillingAddress.city` | `city` | City |
| `defaultBillingAddress.zipcode` | `zip` | Postal code |
| `defaultBillingAddress.countryId` | `country_id` | Country reference |
| `defaultBillingAddress.countryStateId` | `state_id` | State/province reference |
| `defaultBillingAddress.phoneNumber` | `phone` | Phone number |

**Custom Values Added:**
- Name splitting logic for firstName/lastName
- Account type mapping (business/private)
- Default customer group assignment
- Default sales channel assignment
- Default payment method assignment
- Auto-generated customer number
- Default password for new customers

---

## 6. Customer Address Module (`customer_address`)

**Odoo Model:** `res.partner` (child records)

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `street` | `street` | Street address |
| `additionalAddressLine1` | `street2` | Additional address line |
| `city` | `city` | City |
| `zipcode` | `zip` | Postal code |
| `countryId` | `country_id` | Country reference |
| `countryStateId` | `state_id` | State/province reference |
| `phoneNumber` | `phone` | Phone number |

**Custom Values Added:**
- Parent customer relationship maintained
- Address type handling (billing/shipping)

---

## 7. Property/Attribute Module (`attribute`)

**Odoo Model:** `product.attribute`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Attribute name |
| `displayType` | `display_type` | Display type (converted: media/text → pills) |

**Custom Values Added:**
- Display type conversion logic
- Filterable property set to true
- Sorting type set to alphanumeric

---

## 8. Property Value Module (`attribute_value`)

**Odoo Model:** `product.attribute.value`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `name` | `name` | Attribute value name |
| `propertyGroupId` | `attribute_id` | Parent attribute reference |

**Custom Values Added:**
- Automatic parent attribute relationship
- Value synchronization with parent attribute

---

## 9. Order Module (`order`)

**Odoo Model:** `sale.order`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `customerId` | `partner_id` | Customer reference |
| `billingAddressId` | `partner_invoice_id` | Billing address reference |
| `shippingAddressId` | `partner_shipping_id` | Shipping address reference |
| `stateMachineState` | `state` | Order status |

**Custom Values Added:**
- Order status mapping between Shopware and Odoo states
- Customer and address relationship handling
- Order line items synchronization

---

## 10. Delivery Module (`delivery`)

**Odoo Model:** `stock.picking`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `stateMachineState` | `state` | Delivery status |
| Order reference | `sale_id` | Related sales order |
| Shipping address | `partner_id` | Delivery partner |
| Order number | `origin` | Source reference |

**Custom Values Added:**
- Delivery action mapping (confirm, validate, cancel)
- Stock move management
- Delivery type assignment (outgoing)
- Move line items creation

---

## 11. Transaction Module (`transaction`)

**Odoo Model:** `account.move` (invoices) / `account.payment`

| Shopware Field | Odoo Field | Description |
|----------------|------------|-------------|
| `stateMachineState` | `payment_state` | Payment status |
| Order reference | Related invoice | Invoice association |

**Custom Values Added:**
- Payment action mapping (paid, cancel, refund)
- Invoice state management (draft, posted, cancel)
- Payment validation and processing
- Version-specific handling (v17 vs older versions)

---

## Field Automation Features

The plugin includes automation for the following Odoo fields:
- **Category fields:** `parent_id`, `name`
- **Product fields:** `list_price`, `categ_id`, `description`, `qty_available`, `attribute_line_ids`
- **Property fields:** `active`, `value_ids`
- **Customer fields:** `email`, `function`, `vat`, `street`, `street2`, `city`, `zip`, `country_id`, `state_id`, `phone`

## Synchronization Direction

- **Shopware → Odoo:** All modules support this direction
- **Odoo → Shopware:** Supported for products, customers, categories, and properties
- **Bidirectional:** Full synchronization with conflict resolution

## Notes

1. **Complex Transformations:** Price calculations include tax conversion, currency handling
2. **Relationship Management:** Parent-child relationships are maintained across both systems
3. **Data Validation:** Field validation and error handling for data integrity
4. **Batch Processing:** Bulk operations supported for performance optimization
5. **Version Compatibility:** Handles different Odoo versions (v17 and older)
